#!/usr/bin/env python3
"""
Script to add missing task_set_id and user_id fields to curated_content_items.
Each curated content item needs to reference its parent set and have a user_id.
"""

import asyncio
import time
from pymongo import AsyncMongoClient
from bson import ObjectId

# Database connection
DB_URL = "mongodb+srv://diwas:<EMAIL>/test_nepali_app"



client=AsyncMongoClient(DB_URL)

async def main():
    all_set = await client.test_nepali_app.curated_content_set.find().to_list(length=None)
    print(f"Found {len(all_set)} curated content sets to process")

    # Process all sets in parallel
    for set in all_set:
        await process_item(set)
    print("✅ All sets processed successfully!")

async def process_item(items):
    root_id = ObjectId(items.get("_id"))
    user_id = ObjectId("68391d86b8b0e7ec9ababfbb")
    tasks = items.get("tasks", [])

    # Create all update operations for parallel execution
    update_operations = []

    # Update the set with user_id
    update_operations.append(
        client.test_nepali_app.curated_content_set.update_one(
            {"_id": root_id},
            {"$set": {"user_id": user_id}}
        )
    )

    # Update all task items in parallel
    for task in tasks:
        update_operations.append(
            client.test_nepali_app.curated_content_items.update_one(
                {"_id": ObjectId(task) if isinstance(task, str) else task},
                {"$set": {"task_set_id": root_id, "user_id": user_id}}
            )
        )

    # Execute all updates for this set in parallel
    await asyncio.gather(*update_operations)
    print(f"✅ Processed set {root_id} with {len(tasks)} items")

if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
