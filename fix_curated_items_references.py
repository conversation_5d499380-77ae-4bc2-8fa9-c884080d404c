#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add missing task_set_id and user_id fields to curated_content_items.
Each curated content item needs to reference its parent set and have a user_id.
"""

import pymongo
from datetime import datetime, timezone
from bson import ObjectId

# Database connection
DB_URL = "mongodb+srv://diwas:<EMAIL>/test_nepali_app"

def fix_curated_items_references():
    """Add task_set_id and user_id to all curated content items."""
    
    # Connect to database
    client = pymongo.MongoClient(DB_URL)
    db = client.get_default_database()
    
    try:
        print("=" * 80)
        print("FIXING CURATED CONTENT ITEMS REFERENCES")
        print("=" * 80)
        
        # Get all curated content sets
        curated_sets = list(db.curated_content_set.find({}))
        print(f"Found {len(curated_sets)} curated content sets")
        
        if len(curated_sets) == 0:
            print("❌ No curated content sets found!")
            return
        
        # For this fix, we'll use a default user_id (you can change this to actual user IDs later)
        # Create a default user_id for the curated content
        default_user_id = ObjectId("507f1f77bcf86cd799439011")  # Example ObjectId
        
        total_items_updated = 0
        sets_processed = 0
        
        for curated_set in curated_sets:
            set_id = curated_set["_id"]
            task_ids = curated_set.get("tasks", [])
            
            if not task_ids:
                print(f"⚠️ Set {set_id} has no task IDs")
                continue
            
            # Convert string task IDs to ObjectIds for querying
            task_object_ids = []
            for task_id in task_ids:
                if isinstance(task_id, str):
                    try:
                        task_object_ids.append(ObjectId(task_id))
                    except:
                        print(f"⚠️ Invalid ObjectId: {task_id}")
                        continue
                else:
                    task_object_ids.append(task_id)
            
            if not task_object_ids:
                print(f"⚠️ Set {set_id} has no valid task ObjectIds")
                continue
            
            # Update all items that belong to this set
            update_result = db.curated_content_items.update_many(
                {"_id": {"$in": task_object_ids}},
                {
                    "$set": {
                        "task_set_id": set_id,
                        "user_id": default_user_id,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            
            items_updated = update_result.modified_count
            total_items_updated += items_updated
            sets_processed += 1
            
            print(f"✓ Set {set_id}: Updated {items_updated} items")
        
        print("\n" + "=" * 80)
        print("FIX COMPLETED!")
        print("=" * 80)
        print(f"✓ Processed {sets_processed} curated content sets")
        print(f"✓ Updated {total_items_updated} curated content items")
        print(f"✓ Added task_set_id and user_id fields to all items")
        
        # Verify the fix
        verify_fix(db)
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()

def verify_fix(db):
    """Verify that all curated content items now have the required fields."""
    
    print("\n" + "=" * 80)
    print("VERIFICATION")
    print("=" * 80)
    
    # Count items with and without the required fields
    total_items = db.curated_content_items.count_documents({})
    
    items_with_task_set_id = db.curated_content_items.count_documents({
        "task_set_id": {"$exists": True}
    })
    
    items_with_user_id = db.curated_content_items.count_documents({
        "user_id": {"$exists": True}
    })
    
    items_with_both = db.curated_content_items.count_documents({
        "task_set_id": {"$exists": True},
        "user_id": {"$exists": True}
    })
    
    print(f"Total curated content items: {total_items}")
    print(f"Items with task_set_id: {items_with_task_set_id}")
    print(f"Items with user_id: {items_with_user_id}")
    print(f"Items with both fields: {items_with_both}")
    
    if items_with_both == total_items:
        print("\n✅ SUCCESS: All curated content items now have task_set_id and user_id!")
    else:
        missing_count = total_items - items_with_both
        print(f"\n⚠️ WARNING: {missing_count} items are still missing required fields")
    
    # Show sample items
    print("\nSample curated content items after fix:")
    samples = list(db.curated_content_items.find(
        {}, 
        {"_id": 1, "task_set_id": 1, "user_id": 1, "question.text": 1}
    ).limit(3))
    
    for i, sample in enumerate(samples, 1):
        question_text = sample.get("question", {}).get("text", "N/A")[:50] + "..."
        task_set_id = sample.get("task_set_id", "Missing")
        user_id = sample.get("user_id", "Missing")
        
        print(f"{i}. Item ID: {sample['_id']}")
        print(f"   Question: {question_text}")
        print(f"   Task Set ID: {task_set_id}")
        print(f"   User ID: {user_id}")
        print()

def update_with_real_user_ids():
    """
    Optional function to update with real user IDs later.
    You can modify this to assign different user IDs based on your requirements.
    """
    client = pymongo.MongoClient(DB_URL)
    db = client.get_default_database()
    
    try:
        print("=" * 80)
        print("UPDATING WITH REAL USER IDs (OPTIONAL)")
        print("=" * 80)
        
        # Example: Assign different user IDs to different themes
        themes = list(db.themes.find({}, {"_id": 1, "name": 1}))
        
        for i, theme in enumerate(themes):
            # Create a unique user ID for each theme (or use actual user IDs)
            theme_user_id = ObjectId()
            
            # Find all sets for this theme
            sets_for_theme = list(db.curated_content_set.find({"theme_id": theme["_id"]}))
            
            for curated_set in sets_for_theme:
                # Update all items in this set with the theme's user ID
                db.curated_content_items.update_many(
                    {"task_set_id": curated_set["_id"]},
                    {"$set": {"user_id": theme_user_id}}
                )
            
            print(f"✓ Updated theme '{theme['name']}' items with user ID: {theme_user_id}")
        
        print(f"\n✓ Updated all items with theme-specific user IDs")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        client.close()

def main():
    """Main function."""
    fix_curated_items_references()
    
    # Uncomment the line below if you want to assign different user IDs per theme
    # update_with_real_user_ids()

if __name__ == "__main__":
    main()
