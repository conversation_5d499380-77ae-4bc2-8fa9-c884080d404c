#!/usr/bin/env python3
"""
Script to create curated content sets and items that match the exact format of task sets and task items.
"""

import asyncio
from datetime import datetime, timezone
from bson import ObjectId
import pymongo

# Database connection
DB_URL = "mongodb+srv://diwas:<EMAIL>/test_nepali_app"

# Theme IDs from the database
THEMES = {
    "culture": ObjectId("685e2955f928ae494af5e969"),  # नेपाली संस्कृति र परम्परा
    "geography": ObjectId("685e2955f928ae494af5e96a"),  # नेपालको भूगोल र हिमाल
    "history": ObjectId("685e2955f928ae494af5e96b"),  # नेपालको इतिहास र सम्पदा
    "festivals": ObjectId("685e2955f928ae494af5e96c"),  # नेपाली चाडपर्व र उत्सव
    "food": ObjectId("685e2955f928ae494af5e96d"),  # नेपाली खानपान र व्यञ्जन
}

def create_curated_content_sets_and_items():
    """Create curated content sets and items matching task format exactly."""
    
    # Connect to database
    client = pymongo.MongoClient(DB_URL)
    db = client.get_default_database()
    
    now = datetime.now(timezone.utc)
    
    # Curated Content Set 1: Nepali Culture and Traditions
    set1_id = ObjectId()
    item1_ids = [ObjectId() for _ in range(5)]
    
    curated_set_1 = {
        "_id": set1_id,
        "title": "नेपाली संस्कृति र परम्परा",
        "title_en": "Nepali Culture and Traditions",
        "description": "नेपाली संस्कृति र परम्पराका बारेमा प्रश्नहरू",
        "description_en": "Questions about Nepali culture and traditions",
        "theme_id": THEMES["culture"],
        "difficulty_level": 1,
        "total_tasks": 5,
        "total_score": 50,
        "tasks": [str(item_id) for item_id in item1_ids],
        "status": "active",
        "created_at": now,
        "updated_at": now
    }
    
    # Curated Items for Set 1
    curated_items_1 = [
        {
            "_id": item1_ids[0],
            "type": "single_choice",
            "title": "नेपाली संस्कृति र परम्परा",
            "question": {
                "type": "single_choice",
                "text": "नेपालको राष्ट्रिय फूल कुन हो?",
                "translated_text": "What is the national flower of Nepal?",
                "options": {
                    "a": "गुलाफ",
                    "b": "रोडोडेन्ड्रन",
                    "c": "सूर्यमुखी"
                },
                "answer_hint": "Rhododendron"
            },
            "correct_answer": {
                "type": "single_choice",
                "value": "b"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 1,
            "created_at": now,
            "updated_at": now
        },
        {
            "_id": item1_ids[1],
            "type": "multiple_choice",
            "title": "नेपाली संस्कृति र परम्परा",
            "question": {
                "type": "multiple_choice",
                "text": "नेपालका मुख्य जातिहरू कुन कुन हुन्?",
                "translated_text": "What are the main ethnic groups of Nepal?",
                "options": {
                    "a": "ब्राह्मण",
                    "b": "चेत्री",
                    "c": "नेवार",
                    "d": "तामाङ"
                },
                "answer_hint": "Brahmin, Chhetri, Newar"
            },
            "correct_answer": {
                "type": "multiple_choice",
                "value": "a,b,c"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 1,
            "created_at": now,
            "updated_at": now
        },
        {
            "_id": item1_ids[2],
            "type": "single_choice",
            "title": "नेपाली संस्कृति र परम्परा",
            "question": {
                "type": "single_choice",
                "text": "नेपालको राष्ट्रिय पोशाक कुन हो?",
                "translated_text": "What is the national dress of Nepal?",
                "options": {
                    "a": "साडी",
                    "b": "दाउरा सुरुवाल",
                    "c": "कुर्ता"
                },
                "answer_hint": "Daura Suruwal"
            },
            "correct_answer": {
                "type": "single_choice",
                "value": "b"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 1,
            "created_at": now,
            "updated_at": now
        },
        {
            "_id": item1_ids[3],
            "type": "single_choice",
            "title": "नेपाली संस्कृति र परम्परा",
            "question": {
                "type": "single_choice",
                "text": "नेपालको राष्ट्रिय चरा कुन हो?",
                "translated_text": "What is the national bird of Nepal?",
                "options": {
                    "a": "डाँफे",
                    "b": "कौवा",
                    "c": "चील"
                },
                "answer_hint": "Danphe (Himalayan Monal)"
            },
            "correct_answer": {
                "type": "single_choice",
                "value": "a"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 1,
            "created_at": now,
            "updated_at": now
        },
        {
            "_id": item1_ids[4],
            "type": "multiple_choice",
            "title": "नेपाली संस्कृति र परम्परा",
            "question": {
                "type": "multiple_choice",
                "text": "नेपालका मुख्य धर्महरू कुन कुन हुन्?",
                "translated_text": "What are the main religions of Nepal?",
                "options": {
                    "a": "हिन्दू धर्म",
                    "b": "बौद्ध धर्म",
                    "c": "इस्लाम धर्म",
                    "d": "किरात धर्म"
                },
                "answer_hint": "Hinduism, Buddhism"
            },
            "correct_answer": {
                "type": "multiple_choice",
                "value": "a,b"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 1,
            "created_at": now,
            "updated_at": now
        }
    ]
    
    # Insert curated content set 1
    print("Inserting Curated Content Set 1: Nepali Culture and Traditions")
    db.curated_content_set.insert_one(curated_set_1)
    db.curated_content_items.insert_many(curated_items_1)
    print(f"✓ Inserted set {set1_id} with {len(curated_items_1)} items")
    
    # Curated Content Set 2: Nepali Geography and Mountains
    set2_id = ObjectId()
    item2_ids = [ObjectId() for _ in range(4)]
    
    curated_set_2 = {
        "_id": set2_id,
        "title": "नेपालको भूगोल र हिमाल",
        "title_en": "Nepal's Geography and Mountains",
        "description": "नेपालको भूगोल र हिमालका बारेमा प्रश्नहरू",
        "description_en": "Questions about Nepal's geography and mountains",
        "theme_id": THEMES["geography"],
        "difficulty_level": 2,
        "total_tasks": 4,
        "total_score": 40,
        "tasks": [str(item_id) for item_id in item2_ids],
        "status": "active",
        "created_at": now,
        "updated_at": now
    }
    
    curated_items_2 = [
        {
            "_id": item2_ids[0],
            "type": "single_choice",
            "title": "नेपालको भूगोल र हिमाल",
            "question": {
                "type": "single_choice",
                "text": "संसारको सबैभन्दा अग्लो हिमाल कुन हो?",
                "translated_text": "What is the world's highest mountain?",
                "options": {
                    "a": "धौलागिरी",
                    "b": "सगरमाथा",
                    "c": "अन्नपूर्ण"
                },
                "answer_hint": "Mount Everest"
            },
            "correct_answer": {
                "type": "single_choice",
                "value": "b"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 2,
            "created_at": now,
            "updated_at": now
        },
        {
            "_id": item2_ids[1],
            "type": "multiple_choice",
            "title": "नेपालको भूगोल र हिमाल",
            "question": {
                "type": "multiple_choice",
                "text": "नेपालका मुख्य नदीहरू कुन कुन हुन्?",
                "translated_text": "What are the main rivers of Nepal?",
                "options": {
                    "a": "कोशी",
                    "b": "गण्डकी",
                    "c": "कर्णाली",
                    "d": "महाकाली"
                },
                "answer_hint": "Koshi, Gandaki, Karnali"
            },
            "correct_answer": {
                "type": "multiple_choice",
                "value": "a,b,c"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 2,
            "created_at": now,
            "updated_at": now
        },
        {
            "_id": item2_ids[2],
            "type": "single_choice",
            "title": "नेपालको भूगोल र हिमाल",
            "question": {
                "type": "single_choice",
                "text": "नेपालको राजधानी कुन हो?",
                "translated_text": "What is the capital of Nepal?",
                "options": {
                    "a": "पोखरा",
                    "b": "काठमाडौं",
                    "c": "भक्तपुर"
                },
                "answer_hint": "Kathmandu"
            },
            "correct_answer": {
                "type": "single_choice",
                "value": "b"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 2,
            "created_at": now,
            "updated_at": now
        },
        {
            "_id": item2_ids[3],
            "type": "single_choice",
            "title": "नेपालको भूगोल र हिमाल",
            "question": {
                "type": "single_choice",
                "text": "नेपालमा कति वटा प्रदेश छन्?",
                "translated_text": "How many provinces are there in Nepal?",
                "options": {
                    "a": "५",
                    "b": "७",
                    "c": "९"
                },
                "answer_hint": "Seven provinces"
            },
            "correct_answer": {
                "type": "single_choice",
                "value": "b"
            },
            "status": "pending",
            "total_score": 10,
            "scored": 0,
            "submitted": False,
            "is_attempted": False,
            "attempts_count": 0,
            "difficulty_level": 2,
            "created_at": now,
            "updated_at": now
        }
    ]
    
    # Insert curated content set 2
    print("Inserting Curated Content Set 2: Nepal's Geography and Mountains")
    db.curated_content_set.insert_one(curated_set_2)
    db.curated_content_items.insert_many(curated_items_2)
    print(f"✓ Inserted set {set2_id} with {len(curated_items_2)} items")
    
    print("\n" + "="*60)
    print("CURATED CONTENT CREATION COMPLETED!")
    print("="*60)
    print(f"Created 2 curated content sets with a total of {len(curated_items_1) + len(curated_items_2)} items")
    print(f"Set 1 ID: {set1_id}")
    print(f"Set 2 ID: {set2_id}")
    print("\nThese sets match the exact format of your task sets and task items.")
    
    client.close()

if __name__ == "__main__":
    create_curated_content_sets_and_items()
