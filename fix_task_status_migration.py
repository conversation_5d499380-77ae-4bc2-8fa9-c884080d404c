#!/usr/bin/env python3
"""
Migration script to fix task status values in the database.
Converts uppercase status values to lowercase to match the TaskStatus enum.
"""

import asyncio
import os
import sys
from datetime import datetime

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from shared.database import get_database_manager
from shared.db_enums import TaskStatus, VerificationStatus
from shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)


async def migrate_task_status_values():
    """Migrate task status values from uppercase to lowercase."""
    
    # Status mappings
    status_mappings = {
        'PENDING': TaskStatus.PENDING,
        'COMPLETED': TaskStatus.COMPLETED,
        'SKIPPED': TaskStatus.SKIPPED,
        'EXPIRED': TaskStatus.EXPIRED,
        'ALL': TaskStatus.ALL
    }
    
    verification_mappings = {
        'PENDING': VerificationStatus.PENDING,
        'VERIFIED': VerificationStatus.VERIFIED,
        'REJECTED': VerificationStatus.REJECTED
    }
    
    try:
        # Get database manager
        db_manager = get_database_manager()
        await db_manager.initialize()
        
        # Get admin database to find all tenants
        async with db_manager.get_admin_db() as admin_db:
            tenants = await admin_db.tenants.find({}).to_list(length=None)
            
        loggers.info(f"Found {len(tenants)} tenants to migrate")
        
        total_task_sets_updated = 0
        total_task_items_updated = 0
        
        for tenant in tenants:
            tenant_id = str(tenant['_id'])
            tenant_name = tenant.get('name', 'Unknown')
            
            loggers.info(f"Migrating tenant: {tenant_name} ({tenant_id})")
            
            try:
                # Get tenant database
                sync_db, async_db = await db_manager.get_tenant_db(tenant_id)
                
                # Update task_sets collection
                task_sets_updated = 0
                for old_status, new_status in status_mappings.items():
                    result = await async_db.task_sets.update_many(
                        {"status": old_status},
                        {"$set": {"status": new_status, "updated_at": datetime.now()}}
                    )
                    task_sets_updated += result.modified_count
                    if result.modified_count > 0:
                        loggers.info(f"  Updated {result.modified_count} task_sets from {old_status} to {new_status}")
                
                # Update task_items collection
                task_items_updated = 0
                
                # Update task status
                for old_status, new_status in status_mappings.items():
                    result = await async_db.task_items.update_many(
                        {"status": old_status},
                        {"$set": {"status": new_status, "updated_at": datetime.now()}}
                    )
                    task_items_updated += result.modified_count
                    if result.modified_count > 0:
                        loggers.info(f"  Updated {result.modified_count} task_items status from {old_status} to {new_status}")
                
                # Update verification status
                for old_status, new_status in verification_mappings.items():
                    result = await async_db.task_items.update_many(
                        {"verification_status": old_status},
                        {"$set": {"verification_status": new_status, "updated_at": datetime.now()}}
                    )
                    task_items_updated += result.modified_count
                    if result.modified_count > 0:
                        loggers.info(f"  Updated {result.modified_count} task_items verification_status from {old_status} to {new_status}")
                
                total_task_sets_updated += task_sets_updated
                total_task_items_updated += task_items_updated
                
                loggers.info(f"  Tenant {tenant_name}: {task_sets_updated} task_sets, {task_items_updated} task_items updated")
                
            except Exception as e:
                loggers.error(f"Error migrating tenant {tenant_name} ({tenant_id}): {e}")
                continue
        
        loggers.info(f"Migration completed!")
        loggers.info(f"Total task_sets updated: {total_task_sets_updated}")
        loggers.info(f"Total task_items updated: {total_task_items_updated}")
        
        return True
        
    except Exception as e:
        loggers.error(f"Migration failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def verify_migration():
    """Verify that the migration was successful."""
    
    try:
        # Get database manager
        db_manager = get_database_manager()
        await db_manager.initialize()
        
        # Get admin database to find all tenants
        async with db_manager.get_admin_db() as admin_db:
            tenants = await admin_db.tenants.find({}).to_list(length=None)
        
        loggers.info("Verifying migration...")
        
        uppercase_statuses = ['PENDING', 'COMPLETED', 'SKIPPED', 'EXPIRED', 'ALL', 'VERIFIED', 'REJECTED']
        
        for tenant in tenants:
            tenant_id = str(tenant['_id'])
            tenant_name = tenant.get('name', 'Unknown')
            
            try:
                # Get tenant database
                sync_db, async_db = await db_manager.get_tenant_db(tenant_id)
                
                # Check for remaining uppercase statuses in task_sets
                for status in uppercase_statuses:
                    count = await async_db.task_sets.count_documents({"status": status})
                    if count > 0:
                        loggers.warning(f"Tenant {tenant_name}: {count} task_sets still have uppercase status '{status}'")
                
                # Check for remaining uppercase statuses in task_items
                for status in uppercase_statuses:
                    count = await async_db.task_items.count_documents({"status": status})
                    if count > 0:
                        loggers.warning(f"Tenant {tenant_name}: {count} task_items still have uppercase status '{status}'")
                    
                    count = await async_db.task_items.count_documents({"verification_status": status})
                    if count > 0:
                        loggers.warning(f"Tenant {tenant_name}: {count} task_items still have uppercase verification_status '{status}'")
                
            except Exception as e:
                loggers.error(f"Error verifying tenant {tenant_name} ({tenant_id}): {e}")
                continue
        
        loggers.info("Verification completed!")
        return True
        
    except Exception as e:
        loggers.error(f"Verification failed: {e}")
        return False


async def main():
    """Run the migration."""
    print("=" * 60)
    print("TASK STATUS MIGRATION SCRIPT")
    print("=" * 60)
    print("This script will convert uppercase status values to lowercase")
    print("to match the TaskStatus and VerificationStatus enums.")
    print()
    
    # Ask for confirmation
    response = input("Do you want to proceed with the migration? (y/N): ")
    if response.lower() != 'y':
        print("Migration cancelled.")
        return
    
    print("\nStarting migration...")
    
    # Run migration
    success = await migrate_task_status_values()
    
    if success:
        print("\nMigration completed successfully!")
        
        # Verify migration
        print("\nVerifying migration...")
        await verify_migration()
        
        print("\nMigration and verification completed!")
    else:
        print("\nMigration failed! Check the logs for details.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
