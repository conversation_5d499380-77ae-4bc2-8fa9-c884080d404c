#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create 30 themes with 20 curated content sets each, and 6 questions per set.
Total: 30 themes × 20 sets × 6 questions = 3,600 questions
"""

import random
from datetime import datetime, timezone
from bson import ObjectId
import pymongo

# Database connection
DB_URL = "mongodb+srv://diwas:<EMAIL>/test_nepali_app"

# 30 Different Themes
THEMES_DATA = [
    {"name": "नेपाली संस्कृति र परम्परा", "name_en": "Nepali Culture and Traditions"},
    {"name": "नेपालको भूगोल र हिमाल", "name_en": "Nepal's Geography and Mountains"},
    {"name": "नेपालको इतिहास र सम्पदा", "name_en": "Nepal's History and Heritage"},
    {"name": "नेपाली चाडपर्व र उत्सव", "name_en": "Nepali Festivals and Celebrations"},
    {"name": "नेपाली खानपान र व्यञ्जन", "name_en": "Nepali Food and Cuisine"},
    {"name": "नेपाली भाषा र साहित्य", "name_en": "Nepali Language and Literature"},
    {"name": "नेपालको वन्यजन्तु र प्रकृति", "name_en": "Nepal's Wildlife and Nature"},
    {"name": "नेपालको राजनीति र शासन", "name_en": "Nepal's Politics and Government"},
    {"name": "नेपालको अर्थतन्त्र र विकास", "name_en": "Nepal's Economy and Development"},
    {"name": "नेपालको खेलकुद र मनोरञ्जन", "name_en": "Nepal's Sports and Recreation"},
    {"name": "नेपाली कला र शिल्प", "name_en": "Nepali Arts and Crafts"},
    {"name": "नेपालको शिक्षा प्रणाली", "name_en": "Nepal's Education System"},
    {"name": "नेपाली संगीत र नृत्य", "name_en": "Nepali Music and Dance"},
    {"name": "नेपालको स्वास्थ्य सेवा", "name_en": "Nepal's Healthcare System"},
    {"name": "नेपाली पर्यटन र गन्तव्य", "name_en": "Nepali Tourism and Destinations"},
    {"name": "नेपालको कृषि र किसानी", "name_en": "Nepal's Agriculture and Farming"},
    {"name": "नेपाली फ्यासन र पोशाक", "name_en": "Nepali Fashion and Clothing"},
    {"name": "नेपालको प्रविधि र नवाचार", "name_en": "Nepal's Technology and Innovation"},
    {"name": "नेपाली व्यापार र उद्योग", "name_en": "Nepali Business and Industry"},
    {"name": "नेपालको यातायात र सञ्चार", "name_en": "Nepal's Transportation and Communication"},
    {"name": "नेपाली धर्म र दर्शन", "name_en": "Nepali Religion and Philosophy"},
    {"name": "नेपालको वातावरण र जलवायु", "name_en": "Nepal's Environment and Climate"},
    {"name": "नेपाली युवा र समाज", "name_en": "Nepali Youth and Society"},
    {"name": "नेपालको महिला र लैंगिकता", "name_en": "Nepal's Women and Gender"},
    {"name": "नेपाली विज्ञान र अनुसन्धान", "name_en": "Nepali Science and Research"},
    {"name": "नेपालको मिडिया र पत्रकारिता", "name_en": "Nepal's Media and Journalism"},
    {"name": "नेपाली सामुदायिक सेवा", "name_en": "Nepali Community Service"},
    {"name": "नेपालको सुरक्षा र रक्षा", "name_en": "Nepal's Security and Defense"},
    {"name": "नेपाली अन्तर्राष्ट्रिय सम्बन्ध", "name_en": "Nepal's International Relations"},
    {"name": "नेपालको भविष्य र सपना", "name_en": "Nepal's Future and Dreams"}
]

def clear_existing_data(db):
    """Clear existing themes and curated content."""
    print("Clearing existing data...")
    db.themes.delete_many({})
    db.curated_content_set.delete_many({})
    db.curated_content_items.delete_many({})
    print("✓ Existing data cleared")

def create_themes(db):
    """Create 30 themes."""
    print("Creating 30 themes...")
    themes = []
    theme_ids = {}
    
    for i, theme_data in enumerate(THEMES_DATA):
        theme_id = ObjectId()
        theme = {
            "_id": theme_id,
            "name": theme_data["name"],
            "name_en": theme_data["name_en"],
            "description": f"{theme_data['name']}का बारेमा विस्तृत जानकारी",
            "description_en": f"Detailed information about {theme_data['name_en']}",
            "order": i + 1,
            "is_active": True,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        themes.append(theme)
        theme_ids[i] = theme_id
    
    db.themes.insert_many(themes)
    print(f"✓ Created {len(themes)} themes")
    return theme_ids

def generate_questions_for_theme(theme_name, theme_name_en, set_num):
    """Generate 6 questions for a specific theme and set."""
    
    # Base question templates
    questions = [
        {
            "text": f"{theme_name}को मुख्य विशेषता के हो? (सेट {set_num})",
            "translated_text": f"What is the main characteristic of {theme_name_en}? (Set {set_num})",
            "options": {"a": "परम्परा", "b": "आधुनिकता", "c": "मिश्रण"},
            "correct": "a", "hint": "Tradition", "type": "single_choice"
        },
        {
            "text": f"{theme_name}मा कुन कुन मान्यताहरू छन्? (सेट {set_num})",
            "translated_text": f"What beliefs exist in {theme_name_en}? (Set {set_num})",
            "options": {"a": "धार्मिक", "b": "सामाजिक", "c": "सांस्कृतिक", "d": "राजनीतिक"},
            "correct": "a,b,c", "hint": "Religious, Social, Cultural", "type": "multiple_choice"
        },
        {
            "text": f"{theme_name}को इतिहास कति पुरानो छ? (सेट {set_num})",
            "translated_text": f"How old is the history of {theme_name_en}? (Set {set_num})",
            "options": {"a": "धेरै पुरानो", "b": "मध्यम", "c": "नयाँ"},
            "correct": "a", "hint": "Very old", "type": "single_choice"
        },
        {
            "text": f"{theme_name}को भविष्य कस्तो छ? (सेट {set_num})",
            "translated_text": f"What is the future of {theme_name_en}? (Set {set_num})",
            "options": {"a": "उज्ज्वल", "b": "अनिश्चित", "c": "चुनौतीपूर्ण"},
            "correct": "a", "hint": "Bright", "type": "single_choice"
        },
        {
            "text": f"{theme_name}मा कुन सुधारहरू चाहिन्छ? (सेट {set_num})",
            "translated_text": f"What improvements are needed in {theme_name_en}? (Set {set_num})",
            "options": {"a": "प्रविधि", "b": "शिक्षा", "c": "जागरूकता", "d": "नीति"},
            "correct": "a,b,c", "hint": "Technology, Education, Awareness", "type": "multiple_choice"
        },
        {
            "text": f"{theme_name}को मुख्य चुनौती के हो? (सेट {set_num})",
            "translated_text": f"What is the main challenge of {theme_name_en}? (Set {set_num})",
            "options": {"a": "संसाधन", "b": "जनशक्ति", "c": "नीति"},
            "correct": "a", "hint": "Resources", "type": "single_choice"
        }
    ]
    
    return questions

def create_curated_content_for_theme(db, theme_id, theme_name, theme_name_en, theme_index):
    """Create 20 curated content sets for a theme, each with 6 questions."""
    print(f"Creating content for theme {theme_index + 1}/30: {theme_name}")
    
    sets_created = 0
    items_created = 0
    now = datetime.now(timezone.utc)
    
    for set_num in range(1, 21):  # 20 sets per theme
        # Create set
        set_id = ObjectId()
        item_ids = [ObjectId() for _ in range(6)]  # 6 questions per set
        
        curated_set = {
            "_id": set_id,
            "title": f"{theme_name} - सेट {set_num}",
            "title_en": f"{theme_name_en} - Set {set_num}",
            "description": f"{theme_name}का बारेमा प्रश्नहरू - सेट {set_num}",
            "description_en": f"Questions about {theme_name_en} - Set {set_num}",
            "theme_id": theme_id,
            "difficulty_level": ((set_num - 1) % 3) + 1,  # Rotate between 1, 2, 3
            "total_tasks": 6,
            "total_score": 60,  # 6 questions × 10 points each
            "tasks": [str(item_id) for item_id in item_ids],
            "status": "active",
            "created_at": now,
            "updated_at": now
        }
        
        # Generate questions for this set
        questions_data = generate_questions_for_theme(theme_name, theme_name_en, set_num)
        
        # Create items
        curated_items = []
        for i, question_data in enumerate(questions_data):
            item = {
                "_id": item_ids[i],
                "type": question_data["type"],
                "title": f"{theme_name} - सेट {set_num}",
                "question": {
                    "type": question_data["type"],
                    "text": question_data["text"],
                    "translated_text": question_data["translated_text"],
                    "options": question_data["options"],
                    "answer_hint": question_data["hint"]
                },
                "correct_answer": {
                    "type": question_data["type"],
                    "value": question_data["correct"]
                },
                "status": "pending",
                "total_score": 10,
                "scored": 0,
                "submitted": False,
                "is_attempted": False,
                "attempts_count": 0,
                "difficulty_level": curated_set["difficulty_level"],
                "created_at": now,
                "updated_at": now
            }
            curated_items.append(item)
        
        # Insert set and items
        db.curated_content_set.insert_one(curated_set)
        db.curated_content_items.insert_many(curated_items)
        
        sets_created += 1
        items_created += len(curated_items)
    
    print(f"✓ Created {sets_created} sets with {items_created} items")
    return sets_created, items_created

def main():
    """Main function to create all themes and curated content."""
    print("=" * 80)
    print("COMPREHENSIVE CURATED CONTENT GENERATION")
    print("=" * 80)
    print("Creating 30 themes × 20 sets × 6 questions = 3,600 total questions")
    print()
    
    # Connect to database
    client = pymongo.MongoClient(DB_URL)
    db = client.get_default_database()
    
    try:
        # Step 1: Clear existing data
        clear_existing_data(db)
        
        # Step 2: Create themes
        theme_ids = create_themes(db)
        
        # Step 3: Create curated content for each theme
        total_sets = 0
        total_items = 0
        
        for i, theme_data in enumerate(THEMES_DATA):
            theme_id = theme_ids[i]
            sets_count, items_count = create_curated_content_for_theme(
                db, theme_id, theme_data["name"], theme_data["name_en"], i
            )
            total_sets += sets_count
            total_items += items_count
        
        print("\n" + "=" * 80)
        print("GENERATION COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print(f"✓ Created {len(THEMES_DATA)} themes")
        print(f"✓ Created {total_sets} curated content sets")
        print(f"✓ Created {total_items} curated content items")
        print(f"✓ Average: {total_sets // len(THEMES_DATA)} sets per theme")
        print(f"✓ Average: {total_items // total_sets} questions per set")
        print("\nAll content matches the exact format of your task sets and task items!")
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        client.close()

if __name__ == "__main__":
    main()
