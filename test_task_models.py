#!/usr/bin/env python3
"""
Test script to verify TaskItem and TaskSet models are properly aligned.
"""

import sys
import os
from datetime import datetime
from typing import List

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from shared.models.task import TaskItem, TaskSet, Question, Answer
from shared.db_enums import QuizType, TaskStatus, VerificationStatus, InputType
from shared.object_id import PyObjectId
from bson import ObjectId


def test_task_item_model():
    """Test TaskItem model with all required and optional fields."""
    print("Testing TaskItem model...")
    
    # Create a sample question
    question = Question(
        type=QuizType.MULTIPLE_CHOICE,
        text="What is the capital of Nepal?",
        options=["Kathmandu", "Pokhara", "Lalitpur", "Bhaktapur"]
    )
    
    # Create a sample answer
    answer = Answer(
        type=QuizType.MULTIPLE_CHOICE,
        value="Kathmandu"
    )
    
    # Create TaskItem with all fields
    task_item = TaskItem(
        id=PyObjectId(),
        type=QuizType.MULTIPLE_CHOICE,
        title="Geography Question",
        question=question,
        correct_answer=answer,
        user_answer=None,
        story=None,
        status=TaskStatus.PENDING,
        result=None,
        remark=None,
        created_at=datetime.now(),
        submitted_at=None,
        verified_at=None,
        answered_at=None,
        submitted_by=None,
        verified_by=None,
        verification_status=VerificationStatus.PENDING,
        verification_notes=None,
        test_status=None,
        test_results=None,
        total_score=10,
        scored=0,
        submitted=False,
        attempts_count=0,
        is_attempted=False,  # This field was missing and has been added
        difficulty_level=1,
        metadata=None
    )
    
    print(f"✓ TaskItem created successfully with ID: {task_item.id}")
    print(f"  - Status: {task_item.status}")
    print(f"  - Verification Status: {task_item.verification_status}")
    print(f"  - Is Attempted: {task_item.is_attempted}")
    print(f"  - Total Score: {task_item.total_score}")
    print(f"  - Scored: {task_item.scored}")
    
    return task_item


def test_task_set_model():
    """Test TaskSet model with proper task ID handling."""
    print("\nTesting TaskSet model...")
    
    # Create TaskSet with task IDs (not full TaskItem objects)
    task_set = TaskSet(
        id=PyObjectId(),
        user_id="user123",
        input_type=InputType.TEXT,
        input_content="Sample input content",
        input_data=None,
        tasks=["task_id_1", "task_id_2", "task_id_3"],  # List of task IDs
        created_at=datetime.now(),
        submitted_at=None,
        verified_at=None,
        completed_at=None,
        status=TaskStatus.PENDING,
        difficulty_level=1,
        gentype="primary",
        is_followup=False,
        parent_task_set_id=None,
        total_tasks=3,
        attempted_tasks=0,
        total_verified=0,
        created_by=None,
        verified_by=None,
        total_score=30,  # 3 tasks * 10 points each
        scored=0,
        attempts_count=0,
        remark=None
    )
    
    print(f"✓ TaskSet created successfully with ID: {task_set.id}")
    print(f"  - User ID: {task_set.user_id}")
    print(f"  - Input Type: {task_set.input_type}")
    print(f"  - Status: {task_set.status}")
    print(f"  - Tasks: {task_set.tasks}")  # Should be list of strings
    print(f"  - Total Tasks: {task_set.total_tasks}")
    print(f"  - Total Score: {task_set.total_score}")
    
    return task_set


def test_model_serialization():
    """Test that models can be serialized to dict and back."""
    print("\nTesting model serialization...")
    
    # Create a TaskItem
    task_item = test_task_item_model()
    
    # Serialize to dict
    task_dict = task_item.model_dump()
    print(f"✓ TaskItem serialized to dict with {len(task_dict)} fields")
    
    # Check critical fields are present
    critical_fields = [
        'id', 'type', 'question', 'correct_answer', 'status', 
        'verification_status', 'total_score', 'scored', 'submitted', 
        'attempts_count', 'is_attempted'
    ]
    
    missing_fields = [field for field in critical_fields if field not in task_dict]
    if missing_fields:
        print(f"✗ Missing critical fields: {missing_fields}")
        return False
    else:
        print(f"✓ All critical fields present: {critical_fields}")
    
    # Create a TaskSet
    task_set = test_task_set_model()
    
    # Serialize to dict
    task_set_dict = task_set.model_dump()
    print(f"✓ TaskSet serialized to dict with {len(task_set_dict)} fields")
    
    # Check that tasks field is list of strings
    if isinstance(task_set_dict['tasks'], list) and all(isinstance(t, str) for t in task_set_dict['tasks']):
        print("✓ TaskSet.tasks is correctly a list of strings")
    else:
        print(f"✗ TaskSet.tasks should be list of strings, got: {type(task_set_dict['tasks'])}")
        return False
    
    return True


def main():
    """Run all tests."""
    print("=" * 60)
    print("TASK MODEL VALIDATION TESTS")
    print("=" * 60)
    
    try:
        # Test individual models
        test_task_item_model()
        test_task_set_model()
        
        # Test serialization
        if test_model_serialization():
            print("\n" + "=" * 60)
            print("✓ ALL TESTS PASSED - Models are properly aligned!")
            print("=" * 60)
            return True
        else:
            print("\n" + "=" * 60)
            print("✗ SOME TESTS FAILED - Check the output above")
            print("=" * 60)
            return False
            
    except Exception as e:
        print(f"\n✗ ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
