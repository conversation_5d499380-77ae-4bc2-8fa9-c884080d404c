#!/usr/bin/env python3
"""
Script to check current task status values in the database.
"""

import asyncio
import os
import sys

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

from shared.database import get_database_manager
from shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)


async def check_status_values():
    """Check current status values in the database."""
    
    try:
        # Get database manager
        db_manager = get_database_manager()
        await db_manager.initialize()
        
        # Get admin database to find all tenants
        async with db_manager.get_admin_db() as admin_db:
            tenants = await admin_db.tenants.find({}).to_list(length=None)
            
        loggers.info(f"Found {len(tenants)} tenants")
        
        all_task_set_statuses = set()
        all_task_item_statuses = set()
        all_verification_statuses = set()
        
        for tenant in tenants:
            tenant_id = str(tenant['_id'])
            tenant_name = tenant.get('name', 'Unknown')
            
            loggers.info(f"Checking tenant: {tenant_name} ({tenant_id})")
            
            try:
                # Get tenant database
                sync_db, async_db = await db_manager.get_tenant_db(tenant_id)
                
                # Get unique status values from task_sets
                pipeline = [{"$group": {"_id": "$status"}}, {"$sort": {"_id": 1}}]
                cursor = await async_db.task_sets.aggregate(pipeline)
                task_set_statuses = [doc["_id"] for doc in await cursor.to_list(length=None) if doc["_id"]]
                all_task_set_statuses.update(task_set_statuses)
                
                # Get unique status values from task_items
                pipeline = [{"$group": {"_id": "$status"}}, {"$sort": {"_id": 1}}]
                cursor = await async_db.task_items.aggregate(pipeline)
                task_item_statuses = [doc["_id"] for doc in await cursor.to_list(length=None) if doc["_id"]]
                all_task_item_statuses.update(task_item_statuses)
                
                # Get unique verification_status values from task_items
                pipeline = [{"$group": {"_id": "$verification_status"}}, {"$sort": {"_id": 1}}]
                cursor = await async_db.task_items.aggregate(pipeline)
                verification_statuses = [doc["_id"] for doc in await cursor.to_list(length=None) if doc["_id"]]
                all_verification_statuses.update(verification_statuses)
                
                loggers.info(f"  Task set statuses: {task_set_statuses}")
                loggers.info(f"  Task item statuses: {task_item_statuses}")
                loggers.info(f"  Verification statuses: {verification_statuses}")
                
            except Exception as e:
                loggers.error(f"Error checking tenant {tenant_name} ({tenant_id}): {e}")
                continue
        
        print("\n" + "=" * 60)
        print("SUMMARY OF STATUS VALUES IN DATABASE")
        print("=" * 60)
        print(f"Task Set Statuses: {sorted(all_task_set_statuses)}")
        print(f"Task Item Statuses: {sorted(all_task_item_statuses)}")
        print(f"Verification Statuses: {sorted(all_verification_statuses)}")
        
        # Check for uppercase values
        uppercase_found = False
        uppercase_statuses = ['PENDING', 'COMPLETED', 'SKIPPED', 'EXPIRED', 'ALL', 'VERIFIED', 'REJECTED']
        
        for status in uppercase_statuses:
            if status in all_task_set_statuses or status in all_task_item_statuses or status in all_verification_statuses:
                uppercase_found = True
                break
        
        if uppercase_found:
            print("\n⚠️  UPPERCASE STATUS VALUES FOUND!")
            print("These need to be migrated to lowercase values.")
            print("Run the migration script: python fix_task_status_migration.py")
        else:
            print("\n✅ All status values are in correct lowercase format!")
        
        return True
        
    except Exception as e:
        loggers.error(f"Check failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the status check."""
    print("=" * 60)
    print("TASK STATUS VALUES CHECK")
    print("=" * 60)
    
    success = await check_status_values()
    
    if not success:
        print("\nCheck failed! See logs for details.")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
