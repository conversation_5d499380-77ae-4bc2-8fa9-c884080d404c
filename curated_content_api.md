# Curated Content API Documentation

**Base URL:** `/v1/management/curated`
**Authentication:** JW<PERSON> Required
**Last Updated:** June 2025

## Overview

The Curated Content API provides access to Nepal-themed educational content organized by themes and content sets. This system manages curated questions and tasks related to Nepali culture, geography, history, and other educational topics.

## Database Collections

- **themes** - Theme categories (culture, geography, history, etc.)
- **curated_content_set** - Content sets containing groups of related questions
- **curated_content_items** - Individual questions/tasks

## API Endpoints

### 1. GET /themes
Get all available themes with optional filtering and pagination.

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 50, max: 100): Items per page
- `search` (string, optional): Search in theme names and descriptions
- `category` (string, optional): Filter by category
- `is_active` (boolean, optional): Filter by active status

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439011",
      "name": "नेपाली संस्कृति",
      "name_en": "Nepali Culture",
      "description": "नेपाली संस्कृति र परम्पराका बारेमा",
      "description_en": "About Nepali culture and traditions",
      "category": "culture",
      "icon": "🏛️",
      "color": "#FF6B6B",
      "is_active": true,
      "created_at": "2024-01-01T10:00:00Z"
    }
  ],
  "meta": {
    "page": 1,
    "limit": 50,
    "total": 25,
    "total_pages": 1
  }
}
```

### 2. GET /themes/{theme_id}
Get all content sets for a specific theme.

**Path Parameters:**
- `theme_id` (string, required): Theme ID

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 100): Items per page
- `difficulty_level` (integer, optional): Filter by difficulty (1=easy, 2=medium, 3=hard)
- `status` (string, optional): Filter by status (pending, completed, etc.)
- `gentype` (string, optional): Filter by generation type (primary, follow_up, etc.)

**Response:**
```json
{
  "data": [
    {
      "_id": "507f1f77bcf86cd799439012",
      "theme_id": "507f1f77bcf86cd799439011",
      "title": "नेपाली त्योहारहरू",
      "title_en": "Nepali Festivals",
      "description": "नेपालका मुख्य त्योहारहरूका बारेमा प्रश्नहरू",
      "difficulty_level": 2,
      "status": "completed",
      "gentype": "primary",
      "task_item_ids": ["507f1f77bcf86cd799439013"],
      "total_items": 10,
      "created_at": "2024-01-01T10:00:00Z",
      "theme": {
        "id": "507f1f77bcf86cd799439011",
        "name": "नेपाली संस्कृति",
        "name_en": "Nepali Culture",
        "icon": "🏛️",
        "color": "#FF6B6B",
        "category": "culture"
      }
    }
  ],
  "meta": {
    "page": 1,
    "limit": 20,
    "total": 15,
    "total_pages": 1
  }
}
```

### 3. GET /filtered
Get filtered content sets across all themes.

**Query Parameters:**
- `page` (integer, default: 1): Page number
- `limit` (integer, default: 20, max: 100): Items per page
- `theme_id` (string, optional): Filter by specific theme ID
- `difficulty_level` (integer, optional): Filter by difficulty (1=easy, 2=medium, 3=hard)
- `status` (string, optional): Filter by status
- `gentype` (string, optional): Filter by generation type

### 4. GET /theme/{theme_id}
Get detailed theme information with statistics.

**Response:**
```json
{
  "success": true,
  "data": {
    "_id": "507f1f77bcf86cd799439011",
    "name": "नेपाली संस्कृति",
    "name_en": "Nepali Culture",
    "category": "culture",
    "statistics": {
      "total_content_sets": 15,
      "total_content_items": 150,
      "average_items_per_set": 10.0
    }
  }
}
```

## Filter Values

### Categories
- `culture`, `geography`, `history`, `language`, `literature`, `science`, `sports`, `politics`, `economy`, `religion`, `art`, `music`, `dance`, `food`, `festivals`, `traditions`, `customs`, `wildlife`, `nature`, `tourism`, `education`, `technology`, `health`, `agriculture`, `business`, `entertainment`

### Status Values
- `pending`, `completed`, `in_progress`, `cancelled`

### Generation Types
- `primary`, `follow_up`, `supplementary`, `review`

### Difficulty Levels
- `1` (Easy), `2` (Medium), `3` (Hard)

## Data Models

### Theme
```json
{
  "_id": "ObjectId",
  "name": "string (Nepali)",
  "name_en": "string (English)",
  "description": "string (Nepali)",
  "description_en": "string (English)",
  "category": "string",
  "icon": "string",
  "color": "string (hex)",
  "is_active": "boolean",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Content Set
```json
{
  "_id": "ObjectId",
  "theme_id": "ObjectId",
  "title": "string (Nepali)",
  "title_en": "string (English)",
  "description": "string (Nepali)",
  "description_en": "string (English)",
  "difficulty_level": "integer (1-3)",
  "status": "string",
  "gentype": "string",
  "task_item_ids": ["array of ObjectIds"],
  "total_items": "integer",
  "metadata": {
    "generated_by": "string",
    "generation_prompt": "string",
    "ai_model": "string",
    "quality_score": "number"
  },
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Content Item
```json
{
  "_id": "ObjectId",
  "question": {
    "text": "string (Nepali)",
    "text_english": "string (English)",
    "type": "string (SINGLE_CHOICE|MULTIPLE_CHOICE|TRUE_FALSE|FILL_BLANK|SHORT_ANSWER|ESSAY|AUDIO_RESPONSE)",
    "options": {
      "a": "string",
      "b": "string",
      "c": "string",
      "d": "string"
    },
    "metadata": {
      "theme_id": "string",
      "difficulty_level": "integer",
      "topic": "string",
      "subtopic": "string",
      "learning_objective": "string"
    }
  },
  "correct_answer": {
    "selected_option": "string",
    "text": "string",
    "explanation": "string (Nepali)",
    "explanation_english": "string (English)"
  },
  "total_score": "integer",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## Error Responses

All endpoints return standard error responses:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {}
  }
}
```

Common error codes:
- `400` - Invalid request parameters
- `401` - Unauthorized (invalid/missing JWT token)
- `404` - Resource not found
- `500` - Internal server error
