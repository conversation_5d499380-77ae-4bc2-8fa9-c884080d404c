"""
Task models for the Management Service.
"""
from pydantic import BaseModel, Field, ConfigDict
from typing import Any, Dict, List, Optional, Union
from bson import ObjectId

from app.shared.object_id import PyObjectId, object_id_field
from app.shared.db_enums import TaskStatus, InputType, QuizType
from app.shared.models.task import Answer, TaskItem
from fastapi import UploadFile
# Service-specific models that extend the shared models

# Response model for task set items in API responses
class TaskSetItem(BaseModel):
    """Model for a task set item in the response."""
    id: PyObjectId = object_id_field()
    user_id: str = Field(description="User ID")
    title: Optional[str] = Field(None, description="Title of the task set")
    input_type: InputType = Field(description="Type of input")
    input_content: Optional[dict] = Field(default={}, description="Content of the input")
    tasks: List[str] = Field(description="List of task IDs")
    status: TaskStatus = Field(description="Status of the task set")

    # Timestamp fields
    created_at: Any = Field(description="Creation date")
    submitted_at: Optional[Any] = Field(None, description="Submission date")
    verified_at: Optional[Any] = Field(None, description="Verification date")
    completed_at: Optional[Any] = Field(None, description="Completion date")

    # Difficulty level from user profile (1=easy, 2=medium, 3=hard)
    difficulty_level: Optional[int] = Field(None, description="Difficulty level used for task generation (1=easy, 2=medium, 3=hard)")

    # Source information
    source: Optional[str] = Field(None, description="Source of the task set (e.g., 'audio', 'text', 'manual')")

    # User reference fields
    created_by: Optional[str] = Field(None, description="User who created the task set")
    verified_by: Optional[str] = Field(None, description="User who verified the task set")

    # Scoring fields - Standardized naming
    total_score: int = Field(default=0, description="Maximum achievable score for all tasks in this set")
    scored: int = Field(default=0, description="Total score earned across all tasks in this set")
    attempted_tasks: int = Field(default=0, description="Number of tasks that have been attempted/submitted")
    total_tasks: int = Field(default=0, description="Total number of tasks in this set")
    attempts_count: int = Field(default=0, description="Number of times this task set has been attempted")

    # Additional information
    notes: Optional[str] = Field(None, description="Additional notes about the task set")
    remark: Optional[str] = Field(None, description="Additional comments about the task set")
    
    # extra
    gentype: Optional[str] = Field(None, description="Generation type: primary or follow_up")
    is_follow_up: Optional[bool] = Field(None, description="Indicates if this is a follow-up task set")

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        populate_by_name=True,
        extra="ignore"
    )


class TaskHistoryItem(BaseModel):
    """Model for a task history item in the response."""
    id: str = Field(alias="_id", description="Task history ID")
    task_set_id: str = Field(description="Task set ID")
    user_id: str = Field(description="User ID")
    input_type: InputType = Field(description="Type of input")
    tasks: List[TaskItem] = Field(description="List of tasks")
    answers: List[Answer] = Field(description="List of answers")
    scores: List[int] = Field(description="List of scores")
    total_score: int = Field(description="Total score achieved")
    created_at: Any = Field(description="Creation date of the task set")
    submitted_at: Any = Field(description="Submission date")
    remark: Optional[str] = Field(None, description="Additional remarks")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Additional metadata")

    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        json_encoders={ObjectId: str},
        populate_by_name=True,
        extra="ignore"
    )


# Request models for API endpoints
class TaskSetRequest(BaseModel):
    set_id: str = Field(..., description="The task set ID")
    include_tasks: bool = Field(
        default=False, description="Whether to include full task details"
    )
    fields: Optional[List[str]] = Field(
        default=["input_type", "input_content", "tasks", "created_at", "status", "total_score", "scored", "attempted_tasks", "total_tasks"],
        description="Fields to retrieve from task set",
    )


class TaskRequest(BaseModel):
    task_id: str = Field(..., description="The task ID")
    fields: Optional[List[str]] = Field(
        default=["type", "question", "story", "correct_answer", "user_answer", "status", "result", "total_score", "score", "submitted", "submitted_at", "attempts_count"],
        description="Fields to retrieve from task",
    )


class TaskSubmissionRequest(BaseModel):
    set_id: str = Field(..., description="The task set ID")
    answers: List[Answer] = Field(..., description="The answers for each task")


class TaskItemSubmissionRequest(BaseModel):
    task_id: str = Field(..., description="The task ID")
    answer: Union[str, List[str]] = Field(..., description="The answer for the task")
    task_type: QuizType = Field(..., description="The type of task")
    folder: Optional[str] = Field(None, description="The folder for file storage (deprecated, use MinioObject.folder instead)")