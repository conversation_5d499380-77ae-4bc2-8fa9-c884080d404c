from fastapi import APIRouter, Depends, HTTPException, Query
from typing import Dict, List, Any, Optional
from bson import ObjectId
from app.shared.security import get_tenant_info
from app.shared.models.user import UserTenantDB
from app.shared.utils.logger import setup_new_logging

loggers = setup_new_logging(__name__)

router = APIRouter(
)


@router.get("/questions/{curated_content_id}", response_model=List[Dict[str, Any]])
async def get_question(
    curated_content_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """

    Fetch a questions by ID.

    Args:
        curated_content_id: The task item ID
        current_user: Current user information

    Returns:
        The question object with selected fields
    """
    try:
        loggers.info(f"Getting question {curated_content_id} for user {current_user.user.username}")

        # Validate task_item_id
        if not curated_content_id or not ObjectId.is_valid(curated_content_id):
            raise HTTPException(status_code=400, detail="Invalid question ID")

        # Get the question
        curated_content_set = await current_user.async_db.curated_content_set.find_one(
            {"_id": ObjectId(curated_content_id)},
            {"tasks"}
        )

        if not curated_content_set:
            raise HTTPException(status_code=404, detail=f"curated_content_set {curated_content_id} not found")



        # Get all task items for this task set
        task_items = await current_user.async_db.curated_content_items.find(
            {"_id": {"$in": [ObjectId(tid) for tid in curated_content_set.get("tasks", [])]}},
            {"question.text": 1, "question.translated_text": 1, "question.type": 1,"_id": 0}
        ).to_list(length=None)

        return task_items
    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error getting question: {str(e)}")



@router.post("/convert/{curated_content_set_id}")
async def convert_curated_to_task_set(
    curated_content_set_id: str,
    current_user: UserTenantDB = Depends(get_tenant_info)
):
    """
    Convert curated content set to individual task set for user.

    Returns:
    - 200: New task set created
    - 201: Task set already exists for user
    - 400: Invalid curated content set ID
    - 404: Curated content set not found
    - 500: Server error
    """
    try:
        import asyncio
        from datetime import datetime, timezone

        # Validate curated content set ID
        if not ObjectId.is_valid(curated_content_set_id):
            raise HTTPException(status_code=400, detail="Invalid curated content set ID format")

        curated_set_oid = ObjectId(curated_content_set_id)

        # Check if user already has this curated content as task set
        existing_task_set = await current_user.async_db.task_sets.find_one({
            "source_curated_set_id": ObjectId(curated_content_set_id),
            "user_id": ObjectId(current_user.user.id),
            "gentype": "curated"
        })

        if existing_task_set:
            return {"task_set_id": str(existing_task_set["_id"])}, 201

        # Parallel fetch of curated content set and items
        curated_set= await current_user.async_db.curated_content_set.find_one({"_id": curated_set_oid})
        if not curated_set:
            raise HTTPException(status_code=404, detail="Curated content set not found")


        tasks_items=await get_curated_items([ObjectId(tid) for tid in curated_set.get("tasks", [])],current_user)
        new_task_set_id = ObjectId()
        new_item_ids = [i.get("_id") for i in tasks_items]

        now = datetime.now(timezone.utc)

        # Prepare new task set
        new_task_set = {
            "_id": new_task_set_id,
            "source_curated_set_id": ObjectId(curated_content_set_id),
            "user_id":ObjectId( current_user.user.id),
            "gentype": "curated",
            "status": "pending",
            "title": curated_set.get("title", ""),
            "theme_id": curated_set.get("theme_id"),
            "title_en": curated_set.get("title_en", ""),
            "description": curated_set.get("description", ""),
            "description_en": curated_set.get("description_en", ""),
            "difficulty_level": curated_set.get("difficulty_level", 1),
            "theme_id": curated_set.get("theme_id"),
            "task_item_ids": [str(item_id) for item_id in new_item_ids],
            "total_tasks": len(new_item_ids),
            "completed_tasks": 0,
            "total_score": sum(item.get("total_score", 10) for item in tasks_items),
            "scored": 0,
            "attempts_count": 0,
            "created_at": now,
            "updated_at": now
        }

        # Prepare new task items
        new_task_items = []
        for i, curated_item in enumerate(tasks_items):
            new_task_item = {
                "_id": new_item_ids[i],
                "source_curated_item_id": str(curated_item["_id"]),
                "task_set_id": str(new_task_set_id),
                "created_by": current_user.user.id,
                "gentype": "curated",
                "question": curated_item.get("question", {}),
                "correct_answer": curated_item.get("correct_answer", {}),
                "user_answer": None,
                "status": "PENDING",
                "result": None,
                "submitted": False,
                "total_score": curated_item.get("total_score", 10),
                "scored": 0,
                "created_at": now,
                "updated_at": now
            }
            new_task_items.append(new_task_item)

        # Parallel database operations
        await current_user.async_db.task_sets.insert_one(new_task_set)

        await current_user.async_db.task_items.insert_many(new_task_items)

 

        return {"task_set_id": str(new_task_set_id)}, 200

    except HTTPException:
        raise
    except Exception as e:
        loggers.error(f"Error in convert_curated_to_task_set: {e}")
        raise HTTPException(status_code=500, detail="Internal server error")




async def get_curated_items(ids:list[ObjectId],current_user:UserTenantDB):

    tasks_items=await current_user.async_db.curated_content_items.find(
            {"_id": {"$in": ids}},
            {"_id": 0}
        ).to_list(length=None)
    # now make new ids for each
    for item in tasks_items:
        item["_id"]=ObjectId()


    return tasks_items


