# Task Model Alignment Fixes

## Summary
Fixed multiple inconsistencies between TaskItem and TaskSet models and their usage in the codebase to ensure proper validation and data consistency.

## Issues Fixed

### 1. TaskItem Model Issues

#### Missing `is_attempted` Field
- **Problem**: Code referenced `is_attempted` field but it wasn't defined in the TaskItem model
- **Fix**: Added `is_attempted: bool = Field(default=False)` to TaskItem model
- **Files**: `app/shared/models/task.py`

#### Duplicate `submitted_at` Field
- **Problem**: `submitted_at` was defined twice in TaskItem model (lines 96 and 116)
- **Fix**: Removed duplicate definition, kept the one with proper Field configuration
- **Files**: `app/shared/models/task.py`

#### Fixed Title Field Type
- **Problem**: `title:Optional[str]` had missing space and no default
- **Fix**: Changed to `title: Optional[str] = None`
- **Files**: `app/shared/models/task.py`

### 2. TaskSet Model Issues

#### Incorrect Tasks Field Type
- **Problem**: Model defined `tasks: List[TaskItem]` but database operations expected `List[str]` (task IDs)
- **Fix**: Changed to `tasks: List[str] = Field(default_factory=list)`
- **Files**: `app/shared/models/task.py`

### 3. Status Enum Issues

#### Inconsistent Status Values
- **Problem**: Code used string values like `"not_attempted"` and `"pending"` but models used enums
- **Fix**: Updated all status assignments to use proper enum values:
  - `TaskStatus.PENDING` instead of `"not_attempted"` or `"pending"`
  - `VerificationStatus.PENDING` instead of `"pending"`
- **Files**: 
  - `app/v1/api/management_service/routes/tasks/task_items.py`
  - `app/v1/api/management_service/routes/tasks/task_sets.py`

#### Missing Enum Imports
- **Problem**: Files using enum values didn't import the enum classes
- **Fix**: Added imports for `TaskStatus` and `VerificationStatus`
- **Files**: 
  - `app/v1/api/management_service/routes/tasks/task_items.py`
  - `app/v1/api/management_service/routes/tasks/task_sets.py`

### 4. Response Model Issues

#### TaskSetItem Validation Errors
- **Problem**: Response validation failed due to:
  - Missing `input_type` field (required but not present in curated content)
  - `tasks` field being `None` instead of list
  - Status enum mismatch (uppercase vs lowercase)
- **Fix**: 
  - Made `input_type` optional: `Optional[InputType] = Field(None)`
  - Made `tasks` optional with default: `Optional[List[str]] = Field(default_factory=list)`
  - Added status validator to handle uppercase/lowercase conversion
- **Files**: `app/v1/api/management_service/models/task.py`

### 5. Curated Content Conversion Issues

#### Missing Required Fields
- **Problem**: Curated content conversion didn't include required fields for TaskSet
- **Fix**: Added missing fields in curated content conversion:
  - `input_type: InputType.TEXT`
  - `status: TaskStatus.PENDING` (proper enum)
  - `attempted_tasks` instead of `completed_tasks`
  - `is_attempted: False` and `attempts_count: 0` for task items
- **Files**: `app/v1/api/management_service/routes/curated/questions.py`

## New Features Added

### 1. Status Value Validator
Added a field validator to `TaskSetItem` model to handle legacy uppercase status values:
```python
@field_validator("status", mode="before")
@classmethod
def normalize_status(cls, v):
    """Convert uppercase status values to lowercase for compatibility."""
    if isinstance(v, str):
        status_mapping = {
            'PENDING': TaskStatus.PENDING,
            'COMPLETED': TaskStatus.COMPLETED,
            'SKIPPED': TaskStatus.SKIPPED,
            'EXPIRED': TaskStatus.EXPIRED,
            'ALL': TaskStatus.ALL
        }
        return status_mapping.get(v.upper(), v.lower())
    return v
```

### 2. Migration Scripts
Created utility scripts to help with data migration and validation:

#### `fix_task_status_migration.py`
- Migrates uppercase status values to lowercase in existing database records
- Updates both `task_sets` and `task_items` collections
- Handles both `status` and `verification_status` fields
- Includes verification step to confirm migration success

#### `check_task_status.py`
- Checks current status values in the database
- Identifies any remaining uppercase values that need migration
- Provides summary of all status values across all tenants

#### `test_task_models.py`
- Validates that TaskItem and TaskSet models work correctly
- Tests serialization and field validation
- Ensures all critical fields are present and properly typed

## Files Modified

1. **`app/shared/models/task.py`**
   - Fixed TaskItem model fields
   - Fixed TaskSet model tasks field type
   - Added proper field defaults and types

2. **`app/v1/api/management_service/models/task.py`**
   - Made TaskSetItem fields optional where needed
   - Added status value validator
   - Added field_validator import

3. **`app/v1/api/management_service/routes/tasks/task_items.py`**
   - Fixed status enum usage
   - Added missing enum imports

4. **`app/v1/api/management_service/routes/tasks/task_sets.py`**
   - Fixed status enum usage
   - Added missing enum imports

5. **`app/v1/api/management_service/routes/curated/questions.py`**
   - Fixed curated content conversion
   - Added missing required fields
   - Used proper enum values

## Testing

Run the following scripts to verify the fixes:

1. **Check current status**: `python check_task_status.py`
2. **Test models**: `python test_task_models.py`
3. **Migrate data** (if needed): `python fix_task_status_migration.py`

## Expected Results

After these fixes:
- ✅ No more validation errors for TaskSetItem responses
- ✅ Consistent field types between models and database operations
- ✅ Proper enum usage throughout the codebase
- ✅ Backward compatibility with existing data
- ✅ Clean, maintainable model definitions

## Notes

- The status validator provides backward compatibility during the transition period
- Migration script should be run on production data to fix existing uppercase values
- All new data will use proper lowercase enum values
- Models are now properly aligned with actual database usage patterns
